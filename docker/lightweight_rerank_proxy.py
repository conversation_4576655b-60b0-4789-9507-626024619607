#!/usr/bin/env python3
"""
轻量级重排序代理服务
专为SiliconFlow Qwen3-Reranker-8B设计
"""

import os
import json
import logging
from typing import List, Dict, Any
from flask import Flask, request, jsonify
import requests

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

app = Flask(__name__)

# SiliconFlow配置
SILICONFLOW_API_KEY = os.getenv('SILICONFLOW_API_KEY')
SILICONFLOW_BASE_URL = "https://api.siliconflow.cn/v1"
RERANK_MODEL = "Qwen/Qwen3-Reranker-8B"

if not SILICONFLOW_API_KEY:
    logger.error("SILICONFLOW_API_KEY环境变量未设置")
    exit(1)


def call_siliconflow_rerank(query: str, documents: List[str], model: str = RERANK_MODEL) -> List[Dict[str, Any]]:
    """调用SiliconFlow重排序API"""
    headers = {
        "Authorization": f"Bearer {SILICONFLOW_API_KEY}",
        "Content-Type": "application/json"
    }
    
    payload = {
        "model": model,
        "query": query,
        "documents": documents,
        "top_k": len(documents),
        "return_documents": False
    }
    
    try:
        response = requests.post(
            f"{SILICONFLOW_BASE_URL}/rerank",
            json=payload,
            headers=headers,
            timeout=30
        )
        response.raise_for_status()
        
        result = response.json()
        return result.get("results", [])
        
    except requests.RequestException as e:
        logger.error(f"SiliconFlow API调用失败: {str(e)}")
        raise


@app.route('/health', methods=['GET'])
def health_check():
    """健康检查端点"""
    return jsonify({"status": "healthy", "model": RERANK_MODEL})


@app.route('/rerank', methods=['POST'])
def rerank():
    """HuggingFace TEI兼容的重排序端点"""
    try:
        data = request.get_json()
        
        # 提取参数
        query = data.get('query', '')
        texts = data.get('texts', [])
        model_id = data.get('model-id', RERANK_MODEL)
        
        if not query or not texts:
            return jsonify({"error": "缺少必需参数: query 和 texts"}), 400
        
        logger.info(f"处理重排序请求: query='{query[:50]}...', texts_count={len(texts)}")
        
        # 调用SiliconFlow API
        siliconflow_results = call_siliconflow_rerank(query, texts, model_id)
        
        # 转换为HuggingFace TEI格式
        hf_results = []
        for result in siliconflow_results:
            hf_results.append({
                "index": result["index"],
                "score": result["relevance_score"]
            })
        
        logger.info(f"重排序完成，返回{len(hf_results)}个结果")
        return jsonify(hf_results)
        
    except Exception as e:
        logger.error(f"重排序处理失败: {str(e)}")
        return jsonify({"error": f"重排序失败: {str(e)}"}), 500


@app.route('/v1/rerank', methods=['POST'])
def rerank_v1():
    """OpenAI兼容的重排序端点"""
    try:
        data = request.get_json()
        
        # 提取参数
        query = data.get('query', '')
        documents = data.get('documents', [])
        model = data.get('model', RERANK_MODEL)
        top_k = data.get('top_k', len(documents))
        
        if not query or not documents:
            return jsonify({"error": "缺少必需参数: query 和 documents"}), 400
        
        logger.info(f"处理v1重排序请求: query='{query[:50]}...', documents_count={len(documents)}")
        
        # 调用SiliconFlow API
        siliconflow_results = call_siliconflow_rerank(query, documents, model)
        
        # 转换为OpenAI格式
        openai_results = {
            "object": "list",
            "data": [],
            "model": model,
            "usage": {
                "total_tokens": len(query.split()) + sum(len(doc.split()) for doc in documents)
            }
        }
        
        for i, result in enumerate(siliconflow_results[:top_k]):
            openai_results["data"].append({
                "index": result["index"],
                "relevance_score": result["relevance_score"],
                "document": {
                    "text": documents[result["index"]]
                }
            })
        
        logger.info(f"v1重排序完成，返回{len(openai_results['data'])}个结果")
        return jsonify(openai_results)
        
    except Exception as e:
        logger.error(f"v1重排序处理失败: {str(e)}")
        return jsonify({"error": f"重排序失败: {str(e)}"}), 500


if __name__ == '__main__':
    logger.info(f"启动SiliconFlow重排序代理服务，模型: {RERANK_MODEL}")
    app.run(host='0.0.0.0', port=8080, debug=False)
