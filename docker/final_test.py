#!/usr/bin/env python3
"""
最终测试R2R SiliconFlow Qwen3集成
"""

import requests
import json
import time
import tempfile
import os

def test_document_upload():
    """测试文档上传功能"""
    
    print("📄 测试文档上传功能...")
    
    # 创建临时文件
    with tempfile.NamedTemporaryFile(mode='w', suffix='.txt', delete=False, encoding='utf-8') as f:
        f.write("这是一个测试文档，用于验证SiliconFlow Qwen3模型的完整功能。\n")
        f.write("包括embedding向量化、LLM文本生成、以及搜索检索等功能。\n")
        f.write("我们使用中文文本来测试模型的多语言支持能力。")
        temp_file = f.name
    
    try:
        # 上传文件
        with open(temp_file, 'rb') as f:
            files = {'file': ('test.txt', f, 'text/plain')}
            response = requests.post(
                "http://localhost:7272/v3/documents",
                files=files
            )
        
        if response.status_code == 200:
            result = response.json()
            print("✅ 文档上传成功！")
            print(f"   文档ID: {result.get('document_id', 'N/A')}")
            return result.get('document_id')
        else:
            print(f"❌ 文档上传失败: {response.status_code}")
            print(f"   错误信息: {response.text}")
            return None
            
    except Exception as e:
        print(f"❌ 文档上传请求失败: {e}")
        return None
    finally:
        # 清理临时文件
        if os.path.exists(temp_file):
            os.unlink(temp_file)

def test_search():
    """测试搜索功能"""
    
    print("\n🔍 测试搜索功能...")
    
    search_data = {
        "query": "测试文档 Qwen3",
        "limit": 5
    }
    
    try:
        response = requests.post(
            "http://localhost:7272/v3/search",
            headers={"Content-Type": "application/json"},
            json=search_data
        )
        
        if response.status_code == 200:
            result = response.json()
            results = result.get('results', [])
            print("✅ 搜索功能正常！")
            print(f"   找到 {len(results)} 个结果")
            if results:
                print(f"   第一个结果得分: {results[0].get('score', 'N/A')}")
            return True
        else:
            print(f"❌ 搜索失败: {response.status_code}")
            print(f"   错误信息: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ 搜索请求失败: {e}")
        return False

def test_rag():
    """测试RAG问答功能"""
    
    print("\n💬 测试RAG问答功能...")
    
    rag_data = {
        "query": "这个文档是关于什么的？",
        "rag_generation_config": {
            "model": "openai/Qwen/Qwen3-32B",
            "temperature": 0.1
        }
    }
    
    try:
        response = requests.post(
            "http://localhost:7272/v3/retrieval/rag",
            headers={"Content-Type": "application/json"},
            json=rag_data
        )
        
        if response.status_code == 200:
            result = response.json()
            print("✅ RAG问答功能正常！")
            completion = result.get('completion', {})
            if completion:
                print(f"   回答: {completion.get('choices', [{}])[0].get('message', {}).get('content', 'N/A')[:100]}...")
            return True
        else:
            print(f"❌ RAG问答失败: {response.status_code}")
            print(f"   错误信息: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ RAG问答请求失败: {e}")
        return False

def main():
    """主函数"""
    
    print("🎯 R2R SiliconFlow Qwen3 最终集成测试")
    print("=" * 60)
    
    # 等待服务启动
    print("⏳ 等待R2R服务启动...")
    time.sleep(3)
    
    # 测试健康检查
    try:
        health_response = requests.get("http://localhost:7272/v3/health")
        if health_response.status_code == 200:
            print("✅ R2R服务健康检查通过")
        else:
            print(f"❌ R2R服务健康检查失败: {health_response.status_code}")
            return 1
    except Exception as e:
        print(f"❌ 无法连接到R2R服务: {e}")
        return 1
    
    # 执行测试
    results = []
    
    # 1. 测试文档上传（包含embedding）
    doc_id = test_document_upload()
    results.append(doc_id is not None)
    
    if doc_id:
        # 等待文档被索引
        time.sleep(3)
        
        # 2. 测试搜索功能
        search_ok = test_search()
        results.append(search_ok)
        
        # 3. 测试RAG问答功能
        rag_ok = test_rag()
        results.append(rag_ok)
    else:
        results.extend([False, False])
    
    # 输出结果
    print("\n" + "=" * 60)
    print("📊 测试结果总结:")
    print(f"   文档上传 (Embedding): {'✅ 通过' if results[0] else '❌ 失败'}")
    print(f"   搜索功能 (Vector Search): {'✅ 通过' if results[1] else '❌ 失败'}")
    print(f"   RAG问答 (LLM Generation): {'✅ 通过' if results[2] else '❌ 失败'}")
    
    if all(results):
        print("\n🎉 所有测试通过！SiliconFlow Qwen3集成成功！")
        print("\n✅ 修复内容总结:")
        print("   - Embedding模型: 移除openai/前缀，添加drop_params=true")
        print("   - LLM模型: 添加openai/前缀以正确识别provider")
        print("   - API配置: 正确设置SiliconFlow API端点")
        print("   - 参数处理: 解决LiteLLM兼容性问题")
        return 0
    else:
        print("\n❌ 部分测试失败，请检查配置")
        return 1

if __name__ == "__main__":
    exit(main())
