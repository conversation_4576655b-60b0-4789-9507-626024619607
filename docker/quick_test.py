#!/usr/bin/env python3
"""
快速测试R2R SiliconFlow Qwen3功能
"""

import requests
import json
import time

def test_search_existing():
    """测试搜索现有文档"""
    
    print("🔍 测试搜索现有文档...")
    
    search_data = {
        "query": "测试",
        "limit": 5
    }
    
    try:
        response = requests.post(
            "http://localhost:7272/v3/search",
            headers={"Content-Type": "application/json"},
            json=search_data
        )
        
        if response.status_code == 200:
            result = response.json()
            results = result.get('results', [])
            print("✅ 搜索功能正常！")
            print(f"   找到 {len(results)} 个结果")
            if results:
                print(f"   第一个结果得分: {results[0].get('score', 'N/A')}")
                print(f"   第一个结果内容: {results[0].get('text', 'N/A')[:100]}...")
            return True
        else:
            print(f"❌ 搜索失败: {response.status_code}")
            print(f"   错误信息: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ 搜索请求失败: {e}")
        return False

def test_rag_simple():
    """测试简单RAG问答"""
    
    print("\n💬 测试RAG问答功能...")
    
    rag_data = {
        "query": "你好，请介绍一下这个系统",
        "rag_generation_config": {
            "model": "openai/Qwen/Qwen3-32B",
            "temperature": 0.1,
            "max_tokens": 100
        }
    }
    
    try:
        response = requests.post(
            "http://localhost:7272/v3/retrieval/rag",
            headers={"Content-Type": "application/json"},
            json=rag_data
        )
        
        if response.status_code == 200:
            result = response.json()
            print("✅ RAG问答功能正常！")
            completion = result.get('completion', {})
            if completion:
                choices = completion.get('choices', [])
                if choices:
                    content = choices[0].get('message', {}).get('content', 'N/A')
                    print(f"   回答: {content[:200]}...")
            return True
        else:
            print(f"❌ RAG问答失败: {response.status_code}")
            print(f"   错误信息: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ RAG问答请求失败: {e}")
        return False

def test_documents_list():
    """测试文档列表"""
    
    print("\n📋 检查现有文档...")
    
    try:
        response = requests.get("http://localhost:7272/v3/documents")
        
        if response.status_code == 200:
            result = response.json()
            docs = result.get('results', [])
            print(f"✅ 找到 {len(docs)} 个文档")
            
            for i, doc in enumerate(docs[:3]):  # 只显示前3个
                doc_id = doc.get('id', 'N/A')
                status = doc.get('ingestion_status', 'N/A')
                print(f"   文档 {i+1}: {doc_id} (状态: {status})")
            
            return len(docs) > 0
        else:
            print(f"❌ 获取文档列表失败: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ 获取文档列表失败: {e}")
        return False

def main():
    """主函数"""
    
    print("⚡ R2R SiliconFlow Qwen3 快速测试")
    print("=" * 50)
    
    # 等待服务启动
    time.sleep(2)
    
    # 测试健康检查
    try:
        health_response = requests.get("http://localhost:7272/v3/health")
        if health_response.status_code == 200:
            print("✅ R2R服务健康检查通过")
        else:
            print(f"❌ R2R服务健康检查失败: {health_response.status_code}")
            return 1
    except Exception as e:
        print(f"❌ 无法连接到R2R服务: {e}")
        return 1
    
    # 执行测试
    results = []
    
    # 1. 检查文档
    docs_ok = test_documents_list()
    results.append(docs_ok)
    
    # 2. 测试搜索（embedding功能）
    search_ok = test_search_existing()
    results.append(search_ok)
    
    # 3. 测试RAG（LLM功能）
    rag_ok = test_rag_simple()
    results.append(rag_ok)
    
    # 输出结果
    print("\n" + "=" * 50)
    print("📊 测试结果总结:")
    print(f"   文档管理: {'✅ 通过' if results[0] else '❌ 失败'}")
    print(f"   搜索功能 (Embedding): {'✅ 通过' if results[1] else '❌ 失败'}")
    print(f"   RAG问答 (LLM): {'✅ 通过' if results[2] else '❌ 失败'}")
    
    if results[1] and results[2]:  # 搜索和RAG都成功
        print("\n🎉 核心功能测试通过！")
        print("✅ SiliconFlow Qwen3 Embedding功能正常")
        print("✅ SiliconFlow Qwen3 LLM功能正常")
        print("✅ 配置修复成功！")
        return 0
    else:
        print("\n⚠️  部分功能可能有问题")
        return 1

if __name__ == "__main__":
    exit(main())
