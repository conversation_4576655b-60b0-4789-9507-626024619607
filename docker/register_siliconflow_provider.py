#!/usr/bin/env python3
"""
SiliconFlow Provider注册脚本
将自定义SiliconFlow Provider注册到R2R系统中
"""

import sys
import os
import importlib.util
from pathlib import Path

def register_siliconflow_provider():
    """
    注册SiliconFlow Provider到R2R系统
    这个函数需要在R2R启动前调用
    """

    # 获取当前脚本目录
    current_dir = Path(__file__).parent
    provider_file = current_dir / "siliconflow_embedding_provider_enhanced.py"

    if not provider_file.exists():
        print(f"❌ SiliconFlow provider file not found: {provider_file}")
        return False

    try:
        # 动态导入SiliconFlow provider
        spec = importlib.util.spec_from_file_location(
            "siliconflow_provider",
            provider_file
        )
        siliconflow_module = importlib.util.module_from_spec(spec)
        spec.loader.exec_module(siliconflow_module)

        # 获取provider类
        SiliconFlowEmbeddingProvider = siliconflow_module.SiliconFlowEmbeddingProvider
        SiliconFlowEmbeddingConfig = siliconflow_module.SiliconFlowEmbeddingConfig

        # 将provider类添加到全局命名空间，供R2R使用
        globals()['SiliconFlowEmbeddingProvider'] = SiliconFlowEmbeddingProvider
        globals()['SiliconFlowEmbeddingConfig'] = SiliconFlowEmbeddingConfig

        print("✅ SiliconFlow Provider模块加载成功")
        print(f"   - Provider类: {SiliconFlowEmbeddingProvider.__name__}")
        print(f"   - Config类: {SiliconFlowEmbeddingConfig.__name__}")

        return True

    except Exception as e:
        print(f"❌ Provider注册失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def setup_environment():
    """设置环境变量和路径"""

    # 添加当前目录到Python路径
    current_dir = str(Path(__file__).parent)
    if current_dir not in sys.path:
        sys.path.insert(0, current_dir)

    # 尝试从env文件加载环境变量
    env_file = Path(__file__).parent / "env" / "r2r.env"
    if env_file.exists():
        try:
            with open(env_file, 'r', encoding='utf-8') as f:
                for line in f:
                    line = line.strip()
                    if line and not line.startswith('#') and '=' in line:
                        key, value = line.split('=', 1)
                        if value and not os.getenv(key):
                            os.environ[key] = value
            print(f"✅ 从 {env_file} 加载环境变量")
        except Exception as e:
            print(f"⚠️  警告: 无法加载环境文件 {env_file}: {e}")

    # 设置R2R相关环境变量
    os.environ.setdefault('R2R_PROJECT_NAME', 'siliconflow_r2r')

    # 确保API key已设置
    api_key = os.getenv('SILICONFLOW_API_KEY') or os.getenv('OPENAI_API_KEY')
    if not api_key:
        print("⚠️  警告: 未设置SILICONFLOW_API_KEY或OPENAI_API_KEY环境变量")
        return False

    print(f"✅ 环境设置完成，API Key: {api_key[:10]}...")
    return True

if __name__ == "__main__":
    print("🚀 开始注册SiliconFlow Provider...")
    
    if not setup_environment():
        print("❌ 环境设置失败")
        sys.exit(1)
    
    if register_siliconflow_provider():
        print("🎉 SiliconFlow Provider注册完成！")
        print("\n📋 使用说明:")
        print("1. 在配置文件中设置 provider = 'siliconflow'")
        print("2. 确保设置了正确的base_model和rerank_model")
        print("3. 启动R2R服务")
    else:
        print("💥 注册失败，请检查错误信息")
        sys.exit(1)
