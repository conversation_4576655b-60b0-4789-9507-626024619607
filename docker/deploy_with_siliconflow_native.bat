@echo off
echo ========================================
echo R2R SiliconFlow Native Provider Deployment
echo ========================================

echo.
echo Checking configuration files...
if not exist "user_configs\r2r_siliconflow_with_native_rerank.toml" (
    echo ERROR: Configuration file user_configs\r2r_siliconflow_with_native_rerank.toml not found
    pause
    exit /b 1
)

echo.
echo Checking SiliconFlow provider files...
if not exist "siliconflow_embedding_provider_enhanced.py" (
    echo ERROR: SiliconFlow provider file siliconflow_embedding_provider_enhanced.py not found
    pause
    exit /b 1
)

if not exist "register_siliconflow_provider.py" (
    echo ERROR: Provider registration file register_siliconflow_provider.py not found
    pause
    exit /b 1
)

echo.
echo Checking environment file...
if not exist "env\r2r.env" (
    echo ERROR: Environment file env\r2r.env not found
    pause
    exit /b 1
)

echo.
echo Validating environment variables...
python -c "import os; key = os.getenv('SILICONFLOW_API_KEY') or os.getenv('OPENAI_API_KEY'); exit(0 if key else 1)"
if errorlevel 1 (
    echo ERROR: SILICONFLOW_API_KEY or OPENAI_API_KEY environment variable not set
    echo Please set one of these environment variables in env\r2r.env
    pause
    exit /b 1
)

echo.
echo REMINDER: Using SiliconFlow native provider with reranking support
echo - LLM: Qwen/Qwen3-32B
echo - VLM: Qwen/Qwen2.5-VL-72B-Instruct  
echo - Embedding: Qwen/Qwen3-Embedding-8B
echo - Reranking: Qwen/Qwen3-Reranker-8B
echo.

echo Stopping existing containers...
docker-compose down

echo.
echo Cleaning up old containers and images...
docker system prune -f

echo.
echo Pulling latest images...
docker-compose pull

echo.
echo Registering SiliconFlow provider...
python register_siliconflow_provider.py
if errorlevel 1 (
    echo ERROR: Failed to register SiliconFlow provider
    pause
    exit /b 1
)

echo.
echo Setting configuration for native SiliconFlow provider...
set R2R_CONFIG_NAME=r2r_siliconflow_with_native_rerank

echo.
echo Starting services in order...
echo 1. Starting database service...
docker-compose --profile postgres up -d postgres

echo Waiting for database to start...
timeout /t 15 /nobreak > nul

echo 2. Starting MinIO storage service...
docker-compose --profile minio up -d minio

echo Waiting for MinIO to start...
timeout /t 10 /nobreak > nul

echo 3. Starting graph clustering service...
docker-compose up -d graph_clustering

echo Waiting for graph clustering service to start...
timeout /t 10 /nobreak > nul

echo 4. Starting R2R core service with SiliconFlow provider...
docker-compose up -d r2r

echo Waiting for R2R service to start...
timeout /t 20 /nobreak > nul

echo 5. Starting R2R Dashboard...
docker-compose up -d r2r-dashboard

echo.
echo ========================================
echo Deployment Complete!
echo ========================================
echo.
echo Service URLs:
echo - R2R API: http://localhost:7272
echo - R2R Dashboard: http://localhost:7273
echo - PostgreSQL: localhost:5432
echo - MinIO: http://localhost:9001
echo.
echo SiliconFlow Configuration:
echo - Provider: Native SiliconFlow
echo - Reranking: Enabled (Qwen3-Reranker-8B)
echo - Embedding Model: Qwen3-Embedding-8B (1024 dimensions)
echo.
echo Default login credentials:
echo - Email: <EMAIL>
echo - Password: change_me_immediately
echo.
echo Checking service status...
docker-compose ps

echo.
echo Testing SiliconFlow provider functionality...
echo Waiting for services to be fully ready...
timeout /t 10 /nobreak > nul

echo.
echo Performing health check...
curl -f http://localhost:7272/v3/health > nul 2>&1
if errorlevel 1 (
    echo WARNING: R2R health check failed, please check logs
    echo Run: docker-compose logs r2r
) else (
    echo SUCCESS: R2R service is healthy
)

echo.
echo Testing embedding functionality...
python -c "
import requests
import json
try:
    response = requests.get('http://localhost:7272/v3/health', timeout=5)
    if response.status_code == 200:
        print('✅ R2R API is responding')
    else:
        print('⚠️  R2R API returned status:', response.status_code)
except Exception as e:
    print('❌ R2R API test failed:', str(e))
"

echo.
echo If you encounter issues, check logs with:
echo docker-compose logs r2r
echo docker-compose logs r2r-dashboard
echo.
echo To test reranking functionality, upload documents and perform searches
echo The reranking will automatically improve search result relevance
echo.
pause
