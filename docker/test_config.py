#!/usr/bin/env python3
"""
测试R2R配置文件的有效性
"""

import os
import sys
from pathlib import Path

def test_toml_config():
    """测试TOML配置文件"""
    
    print("🔍 测试配置文件...")
    
    try:
        import toml
    except ImportError:
        print("⚠️  警告: 未安装toml库，跳过配置文件解析测试")
        return True
    
    config_file = Path("user_configs/r2r_siliconflow_fixed.toml")
    
    if not config_file.exists():
        print(f"❌ 配置文件不存在: {config_file}")
        return False
    
    try:
        with open(config_file, 'r', encoding='utf-8') as f:
            config = toml.load(f)
        
        print("✅ 配置文件解析成功")
        
        # 检查关键配置
        embedding_config = config.get('embedding', {})
        completion_config = config.get('completion', {})
        app_config = config.get('app', {})
        
        print("\n📋 配置检查:")
        
        # 检查embedding配置
        base_model = embedding_config.get('base_model')
        base_dimension = embedding_config.get('base_dimension')
        
        print(f"   - Embedding模型: {base_model}")
        print(f"   - 向量维度: {base_dimension}")
        
        if base_model and 'openai/' not in base_model:
            print("   ✅ 模型名称格式正确 (无openai/前缀)")
        else:
            print("   ⚠️  模型名称可能有问题")
        
        if str(base_dimension).lower() == 'nan':
            print("   ✅ 维度设置为NaN (自动处理)")
        elif isinstance(base_dimension, (int, float)) and base_dimension > 0:
            print("   ✅ 维度设置为数值")
        else:
            print("   ⚠️  维度设置可能有问题")
        
        # 检查LLM配置
        fast_llm = app_config.get('fast_llm')
        quality_llm = app_config.get('quality_llm')
        
        print(f"   - Fast LLM: {fast_llm}")
        print(f"   - Quality LLM: {quality_llm}")
        
        # 检查API配置
        embedding_gen_config = embedding_config.get('generation_config', {})
        completion_gen_config = completion_config.get('generation_config', {})
        
        api_base = embedding_gen_config.get('api_base') or completion_gen_config.get('api_base')
        drop_params = embedding_gen_config.get('drop_params') or completion_gen_config.get('drop_params')
        
        print(f"   - API Base: {api_base}")
        print(f"   - Drop Params: {drop_params}")
        
        if 'siliconflow.cn' in str(api_base):
            print("   ✅ API端点配置正确")
        else:
            print("   ⚠️  API端点可能有问题")
        
        if drop_params:
            print("   ✅ 启用参数丢弃")
        else:
            print("   ⚠️  未启用参数丢弃")
        
        return True
        
    except Exception as e:
        print(f"❌ 配置文件解析失败: {e}")
        return False

def test_environment():
    """测试环境变量"""
    
    print("\n🌍 测试环境变量...")
    
    # 检查API密钥
    api_key = os.getenv('OPENAI_API_KEY') or os.getenv('SILICONFLOW_API_KEY')
    if api_key:
        print(f"   ✅ API密钥已设置: {api_key[:10]}...")
    else:
        print("   ⚠️  未设置API密钥")
    
    # 检查LiteLLM参数
    drop_params = os.getenv('LITELLM_DROP_PARAMS')
    if drop_params and drop_params.lower() == 'true':
        print("   ✅ LITELLM_DROP_PARAMS=true")
    else:
        print("   ⚠️  未设置LITELLM_DROP_PARAMS")
    
    # 检查配置路径
    config_path = os.getenv('R2R_CONFIG_PATH')
    if config_path:
        print(f"   ✅ 配置路径: {config_path}")
    else:
        print("   ⚠️  未设置R2R_CONFIG_PATH")
    
    return True

def main():
    """主函数"""
    
    print("🧪 R2R SiliconFlow配置测试")
    print("=" * 50)
    
    # 测试配置文件
    config_ok = test_toml_config()
    
    # 测试环境变量
    env_ok = test_environment()
    
    print("\n" + "=" * 50)
    
    if config_ok and env_ok:
        print("✅ 配置测试通过！")
        print("\n📋 建议的启动步骤:")
        print("1. docker-compose down")
        print("2. docker-compose up -d")
        print("3. docker-compose logs -f r2r")
        return 0
    else:
        print("❌ 配置测试失败，请检查配置")
        return 1

if __name__ == "__main__":
    sys.exit(main())
