#!/usr/bin/env python3
"""
测试R2R SiliconFlow Qwen3 Embedding功能
"""

import requests
import json
import time

def test_embedding():
    """测试embedding功能"""
    
    print("🧪 测试R2R SiliconFlow Qwen3 Embedding功能")
    print("=" * 50)
    
    # 等待服务启动
    print("⏳ 等待R2R服务启动...")
    time.sleep(5)
    
    # 测试健康检查
    try:
        health_response = requests.get("http://localhost:7272/v3/health")
        if health_response.status_code == 200:
            print("✅ R2R服务健康检查通过")
        else:
            print(f"⚠️  R2R服务健康检查失败: {health_response.status_code}")
    except Exception as e:
        print(f"❌ 无法连接到R2R服务: {e}")
        return False
    
    # 测试文档上传（这会触发embedding）
    print("\n📄 测试文档上传和embedding...")
    
    test_data = {
        "raw_text": "这是一个测试文档，用于验证SiliconFlow Qwen3模型的embedding功能是否正常工作。我们使用中文文本来测试模型的多语言支持能力。",
        "metadata": {
            "title": "SiliconFlow Qwen3 测试文档",
            "source": "embedding_test"
        }
    }

    # 也尝试使用chunks格式
    test_data_chunks = {
        "chunks": [
            {
                "text": "这是一个测试文档，用于验证SiliconFlow Qwen3模型的embedding功能是否正常工作。",
                "metadata": {
                    "title": "SiliconFlow Qwen3 测试文档",
                    "source": "embedding_test"
                }
            }
        ]
    }
    
    # 首先尝试raw_text格式
    try:
        response = requests.post(
            "http://localhost:7272/v3/documents",
            headers={"Content-Type": "application/json"},
            json=test_data
        )

        if response.status_code == 200:
            result = response.json()
            print("✅ 文档上传成功！(raw_text格式)")
            print(f"   文档ID: {result.get('document_id', 'N/A')}")
            print("   这表明embedding功能正常工作")
            return True
        else:
            print(f"⚠️  raw_text格式失败: {response.status_code}")
            print(f"   错误信息: {response.text}")

    except Exception as e:
        print(f"⚠️  raw_text请求失败: {e}")

    # 尝试chunks格式
    try:
        response = requests.post(
            "http://localhost:7272/v3/documents",
            headers={"Content-Type": "application/json"},
            json=test_data_chunks
        )

        if response.status_code == 200:
            result = response.json()
            print("✅ 文档上传成功！(chunks格式)")
            print(f"   文档ID: {result.get('document_id', 'N/A')}")
            print("   这表明embedding功能正常工作")
            return True
        else:
            print(f"❌ chunks格式也失败: {response.status_code}")
            print(f"   错误信息: {response.text}")
            return False

    except Exception as e:
        print(f"❌ chunks请求失败: {e}")
        return False

def test_search():
    """测试搜索功能（需要embedding）"""
    
    print("\n🔍 测试搜索功能...")
    
    search_data = {
        "query": "测试文档",
        "limit": 5
    }
    
    try:
        response = requests.post(
            "http://localhost:7272/v3/search",
            headers={"Content-Type": "application/json"},
            json=search_data
        )
        
        if response.status_code == 200:
            result = response.json()
            print("✅ 搜索功能正常！")
            print(f"   找到 {len(result.get('results', []))} 个结果")
            return True
        else:
            print(f"⚠️  搜索请求失败: {response.status_code}")
            print(f"   错误信息: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ 搜索请求失败: {e}")
        return False

def main():
    """主函数"""
    
    # 测试embedding
    embedding_ok = test_embedding()
    
    if embedding_ok:
        # 等待一下让文档被索引
        time.sleep(2)
        # 测试搜索
        search_ok = test_search()
    else:
        search_ok = False
    
    print("\n" + "=" * 50)
    
    if embedding_ok and search_ok:
        print("🎉 所有测试通过！")
        print("✅ SiliconFlow Qwen3 Embedding功能正常工作")
        print("✅ 文档上传和搜索功能正常")
        print("\n📋 配置修复成功:")
        print("   - 移除了模型名称中的openai/前缀")
        print("   - 添加了drop_params=true参数")
        print("   - 设置了正确的API端点")
        return 0
    else:
        print("❌ 部分测试失败")
        return 1

if __name__ == "__main__":
    exit(main())
