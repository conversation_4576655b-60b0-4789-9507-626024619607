#!/usr/bin/env python3
"""
简单测试R2R服务状态
"""

import requests
import json

def test_r2r_status():
    """测试R2R服务状态"""
    
    print("🔍 测试R2R服务状态")
    print("=" * 40)
    
    # 测试健康检查
    try:
        response = requests.get("http://localhost:7272/v3/health")
        print(f"健康检查状态码: {response.status_code}")
        if response.status_code == 200:
            print("✅ R2R服务运行正常")
            try:
                health_data = response.json()
                print(f"健康检查响应: {json.dumps(health_data, indent=2, ensure_ascii=False)}")
            except:
                print(f"健康检查响应: {response.text}")
        else:
            print(f"⚠️  健康检查失败: {response.text}")
    except Exception as e:
        print(f"❌ 无法连接到R2R服务: {e}")
        return False
    
    # 测试系统信息
    try:
        response = requests.get("http://localhost:7272/v3/system")
        print(f"\n系统信息状态码: {response.status_code}")
        if response.status_code == 200:
            try:
                system_data = response.json()
                print("✅ 系统信息获取成功")
                print(f"系统信息: {json.dumps(system_data, indent=2, ensure_ascii=False)}")
            except:
                print(f"系统信息响应: {response.text}")
        else:
            print(f"⚠️  系统信息获取失败: {response.text}")
    except Exception as e:
        print(f"⚠️  系统信息请求失败: {e}")
    
    # 测试文档列表
    try:
        response = requests.get("http://localhost:7272/v3/documents")
        print(f"\n文档列表状态码: {response.status_code}")
        if response.status_code == 200:
            try:
                docs_data = response.json()
                print("✅ 文档列表获取成功")
                print(f"文档数量: {len(docs_data.get('results', []))}")
            except:
                print(f"文档列表响应: {response.text}")
        else:
            print(f"⚠️  文档列表获取失败: {response.text}")
    except Exception as e:
        print(f"⚠️  文档列表请求失败: {e}")
    
    return True

if __name__ == "__main__":
    test_r2r_status()
