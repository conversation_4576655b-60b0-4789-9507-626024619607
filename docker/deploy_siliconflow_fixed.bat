@echo off
chcp 65001 >nul
echo ========================================
echo R2R SiliconFlow 部署脚本 (修复版)
echo ========================================
echo.

echo 🔧 设置环境变量...

REM 设置LiteLLM参数丢弃
set LITELLM_DROP_PARAMS=true

REM 设置日志级别
set LITELLM_LOG=DEBUG

echo ✅ 环境变量设置完成
echo    - LITELLM_DROP_PARAMS=true
echo    - LITELLM_LOG=DEBUG
echo.

echo 🐳 启动R2R服务...
echo 使用配置文件: r2r_siliconflow_fixed.toml
echo.

REM 更新环境配置文件
echo R2R_CONFIG_PATH=/app/user_configs/r2r_siliconflow_fixed.toml > env\r2r_temp.env
type env\r2r.env | findstr /v "R2R_CONFIG_PATH" >> env\r2r_temp.env
move env\r2r_temp.env env\r2r.env

REM 启动Docker Compose
docker-compose up -d

echo.
echo ✅ 部署完成！
echo.
echo 📋 服务信息:
echo    - R2R API: http://localhost:7272
echo    - 配置文件: r2r_siliconflow_fixed.toml
echo    - 修复内容: 
echo      * 移除openai/前缀避免模型误识别
echo      * 设置base_dimension为NaN自动处理
echo      * 启用drop_params丢弃不支持的参数
echo.
echo 🔍 查看日志: docker-compose logs -f r2r
echo 🛑 停止服务: docker-compose down
echo.

pause
