#!/usr/bin/env python3
"""
SiliconFlow Provider初始化脚本
在R2R启动时自动加载SiliconFlow provider
"""

import os
import sys
from pathlib import Path

def init_siliconflow_provider():
    """初始化SiliconFlow provider"""
    
    # 添加docker目录到Python路径
    docker_dir = Path(__file__).parent.parent
    if str(docker_dir) not in sys.path:
        sys.path.insert(0, str(docker_dir))
    
    try:
        # 导入SiliconFlow provider
        from siliconflow_embedding_provider_enhanced import (
            SiliconFlowEmbeddingProvider,
            SiliconFlowEmbeddingConfig
        )
        
        # 验证API key
        api_key = os.getenv('SILICONFLOW_API_KEY') or os.getenv('OPENAI_API_KEY')
        if not api_key:
            print("⚠️  警告: 未设置SILICONFLOW_API_KEY或OPENAI_API_KEY")
            return False
        
        print("✅ SiliconFlow Provider初始化成功")
        print(f"   - API Key: {api_key[:10]}...")
        
        return True
        
    except ImportError as e:
        print(f"❌ SiliconFlow Provider导入失败: {e}")
        return False
    except Exception as e:
        print(f"❌ SiliconFlow Provider初始化失败: {e}")
        return False

if __name__ == "__main__":
    init_siliconflow_provider()
