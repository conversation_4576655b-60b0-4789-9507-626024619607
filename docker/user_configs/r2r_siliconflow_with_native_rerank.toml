[app]
# app settings are global available like `r2r_config.agent.app`
# project_name = "r2r_default" # optional, can also set with `R2R_PROJECT_NAME` env var
default_max_documents_per_user = 10_000
default_max_chunks_per_user = 10_000_000
default_max_collections_per_user = 5_000

# Set the default max upload size to 200 GB for local testing
default_max_upload_size = 214748364800

# LLM used for internal operations, like deriving conversation names
fast_llm = "Qwen/Qwen3-32B"

# LLM used for user-facing output, like RAG replies
quality_llm = "Qwen/Qwen3-32B"

# LLM used for ingesting visual inputs - 使用SiliconFlow的视觉模型
vlm = "Qwen/Qwen2.5-VL-72B-Instruct"

# LLM used for transcription
audio_lm = "openai/whisper-1"

# Reasoning model, used for `research` agent
reasoning_llm = "Qwen/Qwen3-32B"
# Planning model, used for `research` agent
planning_llm = "Qwen/Qwen3-32B"

# Evaluation model, used for `research` agent
evaluation_llm = "Qwen/Qwen3-32B"

# Evaluation model, used for `research` agent
web_search_llm = "Qwen/Qwen3-32B"

# Evaluation model, used for `research` agent
web_search_llm = "Qwen/Qwen3-32B"

[auth]
provider = "r2r"
access_token_lifetime_in_minutes = 60000
refresh_token_lifetime_in_days = 7
require_authentication = false
require_email_verification = false
default_admin_email = "<EMAIL>"
default_admin_password = "change_me_immediately"

[completion]
provider = "litellm"
concurrent_request_limit = 64
request_timeout = 60

  [completion.generation_config]
  temperature = 0.1
  top_p = 1
  max_tokens_to_sample = 4_096
  stream = false
  api_base = "https://api.siliconflow.cn/v1/"
  add_generation_kwargs = { }

[crypto]
provider = "bcrypt"

[file]
provider = "postgres"

[database]
default_collection_name = "Default"
default_collection_description = "Your default collection."
collection_summary_prompt = "collection_summary"

  [database.graph_creation_settings]
    graph_entity_description_prompt = "graph_entity_description"
    graph_extraction_prompt = "graph_extraction"
    entity_types = [] # if empty, all entities are extracted
    relation_types = [] # if empty, all relations are extracted
    automatic_deduplication = true # enable automatic deduplication of entities

  [database.graph_enrichment_settings]
    graph_communities_prompt = "graph_communities"

  [database.maintenance]
    vacuum_schedule = "0 3 * * *"  # Run at 3:00 AM daily

# 使用自定义SiliconFlow Provider，支持原生重排序
[embedding]
provider = "siliconflow"
base_model = "Qwen/Qwen3-Embedding-8B"
base_dimension = 1024  # Qwen3-Embedding-8B的向量维度
rerank_model = "Qwen/Qwen3-Reranker-8B"  # 启用重排序
supports_reranking = true
# API key从环境变量自动获取: SILICONFLOW_API_KEY 或 OPENAI_API_KEY
api_base = "https://api.siliconflow.cn/v1"
batch_size = 128
concurrent_request_limit = 256
initial_backoff = 1.0
quantization_settings = { quantization_type = "FP32" }

[completion_embedding]
# 使用相同的自定义provider，但不需要重排序
provider = "siliconflow"
base_model = "Qwen/Qwen3-Embedding-8B"
base_dimension = 1024
api_base = "https://api.siliconflow.cn/v1"
batch_size = 128
concurrent_request_limit = 256

[ingestion]
provider = "r2r"
excluded_parsers = ["mp4"]

[kg]
provider = "postgres"
batch_size = 1
text_splitter = "recursive_character"
text_splitter_config = { chunk_size = 1_024, chunk_overlap = 512 }

[logging]
provider = "local"
log_table = "logs"
log_info_table = "log_info"

[orchestration]
provider = "simple"

[email]
provider = "console_mock" # `smtp`, `sendgrid`, and `mailersend` supported

[scheduler]
provider = "apscheduler"
