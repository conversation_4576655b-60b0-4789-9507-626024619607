#!/usr/bin/env python3
"""
LiteLLM参数修复脚本
解决SiliconFlow Qwen3模型与LiteLLM的兼容性问题
"""

import os
import sys
import logging

def setup_litellm_fixes():
    """设置LiteLLM修复参数"""
    
    print("🔧 正在设置LiteLLM修复参数...")
    
    try:
        # 导入litellm
        import litellm
        
        # 1. 启用参数丢弃 - 关键修复
        litellm.drop_params = True
        print("✅ 启用 litellm.drop_params = True")
        
        # 2. 启用参数修改
        litellm.modify_params = True
        print("✅ 启用 litellm.modify_params = True")
        
        # 3. 设置调试模式
        litellm.set_verbose = True
        print("✅ 启用 litellm.set_verbose = True")
        
        # 4. 添加自定义模型映射
        custom_models = {
            "Qwen/Qwen3-32B": {
                "max_tokens": 32768,
                "max_input_tokens": 32768,
                "max_output_tokens": 8192,
                "input_cost_per_token": 0.0000005,
                "output_cost_per_token": 0.0000015,
                "litellm_provider": "openai",
                "mode": "chat"
            },
            "Qwen/Qwen3-Embedding-8B": {
                "max_tokens": 8192,
                "max_input_tokens": 8192,
                "input_cost_per_token": 0.0000001,
                "litellm_provider": "openai",
                "mode": "embedding",
                "output_vector_size": 1024
            },
            "Qwen/Qwen2.5-VL-72B-Instruct": {
                "max_tokens": 32768,
                "max_input_tokens": 32768,
                "max_output_tokens": 8192,
                "input_cost_per_token": 0.000001,
                "output_cost_per_token": 0.000003,
                "litellm_provider": "openai",
                "mode": "chat",
                "supports_vision": True
            }
        }
        
        # 添加到LiteLLM的模型成本映射
        if hasattr(litellm, 'model_cost'):
            litellm.model_cost.update(custom_models)
            print("✅ 添加自定义Qwen模型映射")
        
        # 5. 设置环境变量
        os.environ['LITELLM_DROP_PARAMS'] = 'true'
        os.environ['LITELLM_LOG'] = 'DEBUG'
        print("✅ 设置环境变量")
        
        print("\n🎉 LiteLLM修复参数设置完成！")
        print("📋 修复内容:")
        print("   - drop_params: 自动丢弃不支持的参数")
        print("   - modify_params: 允许参数修改")
        print("   - 自定义Qwen模型映射")
        print("   - 调试模式启用")
        
        return True
        
    except ImportError:
        print("⚠️  警告: 无法导入litellm，将在R2R启动时应用修复")
        return False
    except Exception as e:
        print(f"❌ 设置LiteLLM修复参数时出错: {e}")
        return False

def create_startup_hook():
    """创建启动钩子文件"""
    
    hook_content = '''
import os
import litellm

# 应用LiteLLM修复
litellm.drop_params = True
litellm.modify_params = True
litellm.set_verbose = True

# 设置环境变量
os.environ['LITELLM_DROP_PARAMS'] = 'true'
os.environ['LITELLM_LOG'] = 'DEBUG'

print("✅ LiteLLM修复参数已应用")
'''
    
    try:
        hook_path = os.path.join(os.getcwd(), 'litellm_fix.py')
        with open(hook_path, 'w', encoding='utf-8') as f:
            f.write(hook_content)
        print(f"✅ 创建启动钩子文件: {hook_path}")
        return True
    except Exception as e:
        print(f"❌ 创建启动钩子文件失败: {e}")
        return False

if __name__ == "__main__":
    print("🚀 LiteLLM参数修复脚本")
    print("=" * 50)
    
    # 设置修复参数
    setup_success = setup_litellm_fixes()
    
    # 创建启动钩子
    hook_success = create_startup_hook()
    
    if setup_success or hook_success:
        print("\n✅ 修复脚本执行完成")
        sys.exit(0)
    else:
        print("\n❌ 修复脚本执行失败")
        sys.exit(1)
