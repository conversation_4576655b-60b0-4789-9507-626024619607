#!/usr/bin/env python3
"""
SiliconFlow OCR代理服务
模拟Mistral OCR API格式，实际调用SiliconFlow视觉模型
"""

import os
import json
import base64
import logging
from typing import Dict, Any, List
from flask import Flask, request, jsonify
import requests

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

app = Flask(__name__)

# SiliconFlow配置
SILICONFLOW_API_KEY = os.getenv('SILICONFLOW_API_KEY')
SILICONFLOW_BASE_URL = "https://api.siliconflow.cn/v1"
VLM_MODEL = "Qwen/Qwen2-VL-72B-Instruct"

if not SILICONFLOW_API_KEY:
    logger.error("SILICONFLOW_API_KEY环境变量未设置")
    exit(1)


def call_siliconflow_vlm(image_base64: str, prompt: str = "请详细描述这张图片中的所有文字内容，保持原有格式。") -> str:
    """
    调用SiliconFlow视觉语言模型进行OCR
    """
    headers = {
        "Authorization": f"Bearer {SILICONFLOW_API_KEY}",
        "Content-Type": "application/json"
    }
    
    payload = {
        "model": VLM_MODEL,
        "messages": [
            {
                "role": "user",
                "content": [
                    {
                        "type": "text",
                        "text": prompt
                    },
                    {
                        "type": "image_url",
                        "image_url": {
                            "url": f"data:image/jpeg;base64,{image_base64}"
                        }
                    }
                ]
            }
        ],
        "max_tokens": 2048,
        "temperature": 0.1
    }
    
    try:
        response = requests.post(
            f"{SILICONFLOW_BASE_URL}/chat/completions",
            json=payload,
            headers=headers,
            timeout=60
        )
        response.raise_for_status()
        
        result = response.json()
        return result["choices"][0]["message"]["content"]
        
    except requests.RequestException as e:
        logger.error(f"SiliconFlow VLM API调用失败: {str(e)}")
        raise


@app.route('/ocr/process', methods=['POST'])
def process_ocr():
    """
    模拟Mistral OCR API的process端点
    """
    try:
        data = request.get_json()
        
        # 提取参数
        model = data.get('model', VLM_MODEL)
        document = data.get('document', {})
        include_image_base64 = data.get('include_image_base64', False)
        
        if not document:
            return jsonify({"error": "缺少document参数"}), 400
        
        logger.info(f"处理OCR请求: model={model}")
        
        # 处理不同类型的文档输入
        if document.get('type') == 'image_url':
            # 从URL获取图片
            image_url = document.get('image_url')
            if not image_url:
                return jsonify({"error": "缺少image_url"}), 400
            
            # 下载图片并转换为base64
            img_response = requests.get(image_url)
            img_response.raise_for_status()
            image_base64 = base64.b64encode(img_response.content).decode('utf-8')
            
        elif document.get('type') == 'document_url':
            # 处理文档URL（假设是图片格式）
            doc_url = document.get('document_url')
            if not doc_url:
                return jsonify({"error": "缺少document_url"}), 400
            
            # 下载文档并转换为base64
            doc_response = requests.get(doc_url)
            doc_response.raise_for_status()
            image_base64 = base64.b64encode(doc_response.content).decode('utf-8')
            
        else:
            return jsonify({"error": "不支持的文档类型"}), 400
        
        # 调用SiliconFlow VLM进行OCR
        ocr_text = call_siliconflow_vlm(image_base64)
        
        # 构造Mistral OCR格式的响应
        response_data = {
            "pages": [
                {
                    "index": 0,
                    "markdown": ocr_text,
                    "image_base64": image_base64 if include_image_base64 else None
                }
            ]
        }
        
        logger.info("OCR处理完成")
        return jsonify(response_data)
        
    except Exception as e:
        logger.error(f"OCR处理错误: {str(e)}")
        return jsonify({"error": f"OCR处理失败: {str(e)}"}), 500


@app.route('/health', methods=['GET'])
def health():
    """
    健康检查端点
    """
    return jsonify({
        "status": "healthy",
        "service": "SiliconFlow OCR代理",
        "model": VLM_MODEL
    })


@app.route('/', methods=['GET'])
def info():
    """
    服务信息端点
    """
    return jsonify({
        "service": "SiliconFlow OCR代理",
        "description": "模拟Mistral OCR API，使用SiliconFlow视觉模型",
        "model": VLM_MODEL,
        "endpoints": {
            "/ocr/process": "POST - OCR处理端点",
            "/health": "GET - 健康检查",
            "/": "GET - 服务信息"
        }
    })


if __name__ == '__main__':
    logger.info("启动SiliconFlow OCR代理服务...")
    logger.info(f"使用VLM模型: {VLM_MODEL}")
    
    app.run(host='0.0.0.0', port=8081, debug=False)
